---
home: true
layout: Blog
icon: house
title: 博客主页
# heroImage: /logo.webp
# heroImageStyle: "border-radius: 50%"
bgImage: https://website-1300358855.cos.ap-guangzhou.myqcloud.com/bgImage.webp
heroText: <PERSON>'s blog
# bgImageStyle: ""
heroFullScreen: true
tagline: 你不需要成为专家，才有资格分享。
projects:
  - icon: /assets/images/fileBrowser.svg
    name: fileBrowser
    desc: 文件浏览器
    link: https://pan.yangqifan.com
    background: "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)"

  - icon: /assets/images/nextChat.svg
    name: next-chat
    desc: 轻量且快速的AI助手
    link: https://chat.yangqifan.com

  # - icon: book
  #   name: 书籍名称
  #   desc: 书籍详细描述
  #   link: https://你的书籍链接

  # - icon: newspaper
  #   name: 文章名称
  #   desc: 文章详细描述
  #   link: https://你的文章链接

  # - icon: user-group
  #   name: 伙伴名称
  #   desc: 伙伴详细介绍
  #   link: https://你的伙伴链接

  # - icon: https://theme-hope-assets.vuejs.press/logo.svg
  #   name: 自定义项目
  #   desc: 自定义详细介绍
  #   link: https://你的自定义链接

# footer: ""
---

<!-- 这是一个博客主页的案例。

要使用此布局，你应该在页面前端设置 `layout: Blog` 和 `home: true`。

相关配置文档请见 [博客主页](https://theme-hope.vuejs.press/zh/guide/blog/home.html)。 -->
