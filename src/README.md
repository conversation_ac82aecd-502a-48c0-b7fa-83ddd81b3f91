---
home: true
layout: Blog
icon: house
title: 博客主页
# heroImage: /logo.webp
# heroImageStyle: "border-radius: 50%"
bgImage: https://website-1300358855.cos.ap-guangzhou.myqcloud.com/bgImage.webp
heroText: <PERSON>'s blog
# bgImageStyle: ""
heroFullScreen: true
tagline: 你不需要成为专家，才有资格分享。
projects:
  - icon: /assets/images/fileBrowser.svg
    name: fileBrowser
    desc: 文件浏览器
    link: https://pan.yangqifan.com
    background: "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)"

  - icon: /assets/images/nextChat.svg
    name: next-chat
    desc: 轻量且快速的AI助手
    link: https://chat.yangqifan.com

  # - icon: book
  #   name: 书籍名称
  #   desc: 书籍详细描述
  #   link: https://你的书籍链接

  # - icon: newspaper
  #   name: 文章名称
  #   desc: 文章详细描述
  #   link: https://你的文章链接

  # - icon: user-group
  #   name: 伙伴名称
  #   desc: 伙伴详细介绍
  #   link: https://你的伙伴链接

  # - icon: https://theme-hope-assets.vuejs.press/logo.svg
  #   name: 自定义项目
  #   desc: 自定义详细介绍
  #   link: https://你的自定义链接

# footer: ""
---

<!-- 这是一个博客主页的案例。

要使用此布局，你应该在页面前端设置 `layout: Blog` 和 `home: true`。

相关配置文档请见 [博客主页](https://theme-hope.vuejs.press/zh/guide/blog/home.html)。 -->
<!-- /* 1. 紫蓝渐变 */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 2. 橙红渐变 */
background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

/* 3. 青绿渐变 */
background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

/* 4. 粉紫渐变 */
background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

/* 5. 金橙渐变 */
background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

/* 6. 蓝紫渐变 */
background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

/* 7. 深蓝渐变 */
background: linear-gradient(135deg, #667db6 0%, #0082c8 100%);

/* 8. 暖橙渐变 */
background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);

/* 9. 紫粉渐变 */
background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);

/* 10. 绿蓝渐变 */
background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);

/* 11. 日落渐变 */
background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);

/* 12. 海洋渐变 */
background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%); -->