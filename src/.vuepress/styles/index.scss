// place your custom styles here
.vp-blog-mask::after {
  background: #999;
  opacity: 0.2;
}

[data-theme="light"] .transparent-navbar .vp-navbar-start a span,
[data-theme="light"] .transparent-navbar .vp-navbar-center a,
[data-theme="light"]
  .transparent-navbar
  .vp-navbar-center
  .vp-dropdown-subtitle,
[data-theme="light"] .transparent-navbar .vp-navbar-center button,
[data-theme="light"] .transparent-navbar .vp-navbar-end .vp-nav-item svg {
  color: #fff;
}

// 专门处理arrow的background-image问题
[data-theme="light"] .transparent-navbar .vp-dropdown-title .arrow,
[data-theme="light"]
  .transparent-navbar
  .external-link-icon
  .external-link:not(.no-external-link-icon)::after {
  filter: brightness(0) invert(1);
}
.vp-navbar .auto-link {
  padding: 0 0.5rem;
}

// 增强滑动按钮的可见性和用户体验
.vp-hero-slide-down-button {
  // 增加按钮的整体高度以容纳文字
  height: 80px !important;
  bottom: 50px !important;

  // 添加悬停效果
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.1);
  }

  // 增强图标的可见性
  .slide-down-icon {
    &:first-child {
      color: rgb(255 255 255 / 40%) !important; // 从15%提升到40%
    }

    &:last-child {
      color: rgb(255 255 255 / 80%) !important; // 从50%提升到80%
    }
  }

  // 添加文字提示
  &::after {
    content: "下滑查看更多";
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    white-space: nowrap;
    pointer-events: none;
    animation: fade-in-out 2s ease-in-out infinite;
  }
}

// 增强动画效果
@keyframes bounce-down {
  0% {
    transform: translateY(-8px);
  }
  50% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(8px);
  }
}

// 文字提示的淡入淡出动画
@keyframes fade-in-out {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

// 导航链接的hover动画效果
.vp-nav-item > .auto-link.route-link-active::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--theme-color, #3eaf7c);
  transform: translateX(-50%);
  transition: width 0.3s ease;
  visibility: visible;
}

.vp-nav-item > .auto-link.route-link-active:hover::before {
  width: 100%;
}

// 下拉菜单图标的弹跳动画
@keyframes bounce {
  0% {
    transform: scale(1) translateY(0);
  }

  10% {
    transform: scale(1.1, 0.9) translateY(0);
  }

  30% {
    transform: scale(0.9, 1.1) translateY(-0.5em);
  }

  50% {
    transform: scale(1.05, 0.95) translateY(0);
  }

  57% {
    transform: scale(1) translateY(-0.125em);
  }

  64% {
    transform: scale(1) translateY(0);
  }

  to {
    transform: scale(1) translateY(0);
  }
}

// 下拉菜单链接的图标弹跳动画
.vp-dropdown-item .auto-link:hover .vp-icon {
  animation-name: bounce;
  animation-duration: 1s;
  animation-timing-function: cubic-bezier(0.28, 0.84, 0.42, 1);
  animation-delay: 0s;
  animation-direction: normal;
}

.vp-navbar .auto-link.route-link-active {
  color: var(--vp-c-text);
}

.vp-project-panel .vp-project-icon {
  width: 25px;
  height: 25px;
}
.vp-project-panel .vp-project-name,
.vp-project-panel .vp-project-desc {
  color: #fff;
}
